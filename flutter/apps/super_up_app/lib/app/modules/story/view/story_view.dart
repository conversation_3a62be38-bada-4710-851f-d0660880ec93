import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_volume_controller/flutter_volume_controller.dart';
import 'package:get_it/get_it.dart';
import 'package:s_translation/generated/l10n.dart';
import 'package:story_view/controller/story_controller.dart';
import 'package:story_view/widgets/story_view.dart';
import 'package:super_up/app/core/api_service/story/story_api_service.dart';
import 'package:super_up/app/core/models/story/story_model.dart';
import 'package:super_up/app/core/models/story/story_reaction_model.dart';
import 'package:super_up/app/core/models/story/story_reply_model.dart';
import 'package:super_up/app/core/models/story/story_view_count_model.dart';
import 'package:super_up/app/modules/memory/controllers/memory_controller.dart';
import 'package:super_up/app/modules/peer_profile/views/peer_profile_view.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';
import 'package:v_platform/v_platform.dart';

import '../../../core/utils/enums.dart';
import 'story_viewers_screen.dart';
import 'widgets/emoji_reaction_picker.dart';

class StoryViewpage extends StatefulWidget {
  final UserStoryModel storyModel;
  final Function(UserStoryModel current)? onComplete;
  final Function()? onDelete;
  final Function(String storyId)? onStoryViewed;

  const StoryViewpage({
    super.key,
    required this.storyModel,
    required this.onComplete,
    required this.onDelete,
    this.onStoryViewed,
  });

  @override
  State<StoryViewpage> createState() => _StoryViewpageState();
}

class _StoryViewpageState extends State<StoryViewpage> {
  final controller = StoryController();

  final stories = <StoryItem>[];
  late StoryModel current = widget.storyModel.stories.first;
  final _api = GetIt.I.get<StoryApiService>();
  final _memoryController = GetIt.I.get<MemoryController>();
  final _replyController = TextEditingController();
  final _replyFocusNode = FocusNode();

  // State for reactions and replies
  String? _selectedEmoji; // Store the selected emoji reaction
  bool _isReacting = false;
  bool _isReplying = false;
  bool _isTypingReply = false; // Track if user is typing a reply
  bool _isMuted = false; // Track mute state for video stories
  double? _originalVolume; // Store original volume to restore later

  // Map to store emoji reactions for each story
  static final Map<String, String> _storyReactions = {};

  // State for view count
  int? _viewsCount;
  bool _isLoadingViewCount = false;

  @override
  void initState() {
    _parseStories();
    super.initState();
    _initializeAudioSession();

    // Add listener to reply focus node to pause/resume story when clicking input field
    _replyFocusNode.addListener(_onReplyFocusChanged);

    // Mark the first story as viewed after the build is complete
    if (widget.storyModel.stories.isNotEmpty) {
      current = widget.storyModel.stories.first;
      // Use a delayed call to ensure the widget tree is fully built
      Future.delayed(Duration.zero, () {
        if (mounted) {
          // Load saved emoji reaction for this story
          setState(() {
            _selectedEmoji = _storyReactions[current.id];
          });
          unawaited(_setSeen(current.id));
          widget.onStoryViewed?.call(current.id);
          // Load view count for own stories
          if (widget.storyModel.userData.isMe) {
            _loadViewCount();
          }
          // Check if user has reacted to this story (for cases where local storage is empty)
          _checkExistingReaction();
        }
      });
    }
  }

  @override
  void dispose() {
    // Restore audio session if it was muted when leaving the story
    if (_isMuted) {
      _restoreAudio();
    }
    controller.dispose();
    _replyController.dispose();
    _replyFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            StoryView(
              onComplete: () {
                context.pop();
                widget.onComplete?.call(widget.storyModel);
              },
              onStoryShow: (storyItem, index) {
                int pos = stories.indexOf(storyItem);
                current = widget.storyModel.stories[pos];
                unawaited(_setSeen(current.id));
                // Reset mute state when story changes
                if (_isMuted) {
                  _restoreAudio();
                  _isMuted = false;
                }
                // Notify the controller that this story was viewed using delayed call
                Future.delayed(Duration.zero, () {
                  if (mounted) {
                    // Load saved emoji reaction for this story
                    setState(() {
                      _selectedEmoji = _storyReactions[current.id];
                    });
                    widget.onStoryViewed?.call(current.id);
                    // Load view count for own stories when story changes
                    if (widget.storyModel.userData.isMe) {
                      _loadViewCount();
                    }
                    // Check for existing reaction
                    _checkExistingReaction();
                  }
                });
              },
              storyItems: stories,
              controller: controller,
            ),
            // Custom caption overlay - positioned differently for owner vs viewer
            if (current.caption != null && current.caption!.isNotEmpty)
              Positioned(
                bottom: widget.storyModel.userData.isMe
                    ? 20 // Story owner: position at bottom of screen for better visibility
                    : 80, // Story viewer: position above reply section (60px reply height + 20px gap)
                left: 20,
                right: 20,
                child: Text(
                  current.caption!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    shadows: [
                      Shadow(
                        offset: Offset(1, 1),
                        blurRadius: 3,
                        color: Colors.black54,
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            Positioned(
              top: 25,
              left: 10,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                mainAxisSize: MainAxisSize.max,
                children: [
                  InkWell(
                    onTap: () {
                      context.pop();
                    },
                    child: const Icon(
                      Icons.arrow_back_ios,
                      color: Colors.white,
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      if (widget.storyModel.userData.isMe) return;
                      context.toPage(
                        PeerProfileView(peerId: widget.storyModel.userData.id),
                      );
                    },
                    child: Row(
                      children: [
                        const SizedBox(
                          width: 10,
                        ),
                        VCircleAvatar(
                          vFileSource: VPlatformFile.fromUrl(
                            networkUrl: widget.storyModel.userData.userImage,
                          ),
                          radius: 20,
                        ),
                        const SizedBox(
                          width: 10,
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            widget.storyModel.userData.fullName.text.black
                                .color(Colors.white),
                            const SizedBox(
                              height: 3,
                            ),
                            format(
                              DateTime.parse(current.createdAt),
                              locale:
                                  Localizations.localeOf(context).languageCode,
                            ).cap.color(Colors.white),
                          ],
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Show view count for own stories - positioned on left side
            if (widget.storyModel.userData.isMe)
              Positioned(
                left: 20,
                top: 100,
                child: GestureDetector(
                  onTap: () {
                    if (!_isLoadingViewCount && (_viewsCount ?? 0) > 0) {
                      context.toPage(
                        StoryViewersScreen(
                          storyId: current.id,
                          storyTitle: 'Story Views',
                        ),
                      );
                    }
                  },
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.visibility,
                          color: Colors.white,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          _isLoadingViewCount
                              ? "..."
                              : "${_viewsCount ?? 0} views",
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            // Mute button for video stories - positioned on right side
            if (current.storyType == StoryType.video)
              Positioned(
                right: 20,
                top: 100,
                child: GestureDetector(
                  onTap: _toggleMute,
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.5),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      _isMuted ? Icons.volume_off : Icons.volume_up,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
              ),
            if (widget.storyModel.userData.isMe)
              Positioned(
                right: 3,
                top: 20,
                child: InkWell(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: PopupMenuButton<int>(
                      icon: Icon(
                        Icons.more_vert_sharp,
                        size: 30,
                        color: Colors.white,
                      ),
                      onSelected: (int result) async {
                        if (result == 1) {
                          final x = await VAppAlert.showAskYesNoDialog(
                            context: context,
                            title: S.of(context).delete,
                            content: S.of(context).areYouSure,
                          );
                          if (x == 1) {
                            await GetIt.I
                                .get<StoryApiService>()
                                .deleteStory(current.id);
                            VAppAlert.showSuccessSnackBar(
                                message: S.of(context).deleted,
                                context: context);
                            if (widget.onDelete != null) {
                              widget.onDelete!();
                            }

                            context.pop();
                          }
                        } else if (result == 2) {
                          // Save to memories
                          await _saveToMemories();
                        }
                      },
                      itemBuilder: (BuildContext context) {
                        List<PopupMenuEntry<int>> items = [];

                        // Only show save to memories for own stories
                        if (widget.storyModel.userData.isMe) {
                          items.add(
                            PopupMenuItem<int>(
                              value: 2,
                              child: Row(
                                children: [
                                  Icon(Icons.bookmark_add, size: 20),
                                  SizedBox(width: 8),
                                  Text('Save to Memories'),
                                ],
                              ),
                            ),
                          );
                          items.add(
                            PopupMenuItem<int>(
                              value: 1,
                              child: Row(
                                children: [
                                  Icon(Icons.delete,
                                      size: 20, color: Colors.red),
                                  SizedBox(width: 8),
                                  Text('Delete',
                                      style: TextStyle(color: Colors.red)),
                                ],
                              ),
                            ),
                          );
                        }

                        return items;
                      },
                    ),
                  ),
                ),
              ),
            // Reaction and Reply UI (only for other people's stories)
            if (!widget.storyModel.userData.isMe)
              Positioned(
                bottom: 10, // Keep it at bottom with small margin
                left: 10,
                right: 10,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Row(
                    children: [
                      // Emoji reaction button
                      GestureDetector(
                        onTap: _showEmojiReactionPicker,
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: _selectedEmoji != null
                                ? Colors.white.withValues(alpha: 0.2)
                                : Colors.transparent,
                            shape: BoxShape.circle,
                          ),
                          child: _isReacting
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white),
                                  ),
                                )
                              : _selectedEmoji != null
                                  ? Text(
                                      _selectedEmoji!,
                                      style: const TextStyle(fontSize: 24),
                                    )
                                  : const Icon(
                                      Icons.emoji_emotions_outlined,
                                      color: Colors.white,
                                      size: 24,
                                    ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Reply input field
                      Expanded(
                        child: TextField(
                          controller: _replyController,
                          focusNode: _replyFocusNode,
                          style: const TextStyle(color: Colors.white),
                          decoration: InputDecoration(
                            hintText:
                                "Reply to ${widget.storyModel.userData.fullName}...",
                            hintStyle: TextStyle(
                                color: Colors.white.withValues(alpha: 0.7)),
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                          ),
                          onSubmitted: (_) => _replyToStory(),
                        ),
                      ),
                      // Send reply button
                      GestureDetector(
                        onTap: _replyToStory,
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          child: _isReplying
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white),
                                  ),
                                )
                              : const Icon(
                                  Icons.send,
                                  color: Colors.white,
                                  size: 24,
                                ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future _setSeen(String id) async {
    vSafeApiCall(
      request: () async {
        await _api.setSeen(current.id);
      },
      onSuccess: (response) {},
    );
  }

  Future<void> _loadViewCount() async {
    if (!widget.storyModel.userData.isMe || _isLoadingViewCount) return;

    setState(() {
      _isLoadingViewCount = true;
    });

    await vSafeApiCall<StoryViewCountModel>(
      request: () async {
        return await _api.getStoryViewsCount(current.id);
      },
      onSuccess: (response) {
        setState(() {
          _viewsCount = response.viewsCount;
        });
      },
      onError: (exception, trace) {
        // Silently handle error - view count is not critical
      },
    );

    setState(() {
      _isLoadingViewCount = false;
    });
  }

  void _showEmojiReactionPicker() {
    if (_isReacting || widget.storyModel.userData.isMe) return;

    // Pause the story when reaction picker is shown
    controller.pause();

    showDialog(
      context: context,
      barrierColor: Colors.transparent,
      builder: (BuildContext context) {
        return EmojiReactionOverlay(
          onEmojiSelected: (emoji) {
            Navigator.of(context).pop();
            // Resume story after selecting emoji
            controller.play();
            _reactToStoryWithEmoji(emoji);
          },
          onCancel: () {
            Navigator.of(context).pop();
            // Resume story when canceling
            controller.play();
          },
        );
      },
    );
  }

  Future<void> _reactToStoryWithEmoji(String emoji) async {
    if (_isReacting || widget.storyModel.userData.isMe) return;

    setState(() {
      _isReacting = true;
    });

    await vSafeApiCall<StoryReactionModel>(
      request: () async {
        return await _api.reactToStory(current.id, emoji: emoji);
      },
      onSuccess: (response) {
        setState(() {
          // Store the selected emoji if liked, clear if unliked
          _selectedEmoji = response.liked ? emoji : null;
          // Save to local storage
          if (response.liked) {
            _storyReactions[current.id] = emoji;
          } else {
            _storyReactions.remove(current.id);
          }
        });

        // Show feedback to user with the selected emoji
        VAppAlert.showSuccessSnackBar(
          message: response.liked ? "$emoji Reacted" : "Reaction removed",
          context: context,
        );
      },
      onError: (exception, trace) {
        VAppAlert.showErrorSnackBar(
          message: "Failed to react to story",
          context: context,
        );
      },
    );

    setState(() {
      _isReacting = false;
    });
  }

  void _toggleMute() async {
    try {
      if (!_isMuted) {
        await _muteAudio();
        setState(() {
          _isMuted = true;
        });
        VAppAlert.showSuccessSnackBar(
          message: "Story muted - Volume set to 0",
          context: context,
        );
      } else {
        await _restoreAudio();
        setState(() {
          _isMuted = false;
        });
        VAppAlert.showSuccessSnackBar(
          message: "Story unmuted - Volume restored",
          context: context,
        );
      }
    } catch (e) {
      // Fallback: just toggle the visual state if audio control fails
      setState(() {
        _isMuted = !_isMuted;
      });
      VAppAlert.showSuccessSnackBar(
        message: "${_isMuted ? 'Muted' : 'Unmuted'} (visual only)",
        context: context,
      );
    }
  }

  /// Check if user has an existing reaction for the current story
  /// This is a fallback for cases where local storage might be empty
  void _checkExistingReaction() {
    // If we already have a stored emoji, no need to check
    if (_selectedEmoji != null) return;

    // For now, we'll rely on the local storage approach
    // In the future, this could make an API call to check reaction status
    // and set a default emoji (like ❤️) if the user has reacted
  }

  Future<void> _replyToStory() async {
    if (_isReplying || widget.storyModel.userData.isMe) return;

    final text = _replyController.text.trim();
    if (text.isEmpty) return;

    setState(() {
      _isReplying = true;
    });

    await vSafeApiCall<StoryReplyResponse>(
      request: () async {
        return await _api.replyToStory(current.id, text);
      },
      onSuccess: (response) async {
        _replyController.clear();

        // Remove focus from text field which will automatically resume the story
        _replyFocusNode.unfocus();

        // Show success feedback
        VAppAlert.showSuccessSnackBar(
          message: "Reply sent",
          context: context,
        );

        // Navigate to chat with the story owner and create a story reply message
        await _navigateToStoryOwnerChat(text);
      },
      onError: (exception, trace) {
        VAppAlert.showErrorSnackBar(
          message: "Failed to send reply",
          context: context,
        );
      },
    );

    setState(() {
      _isReplying = false;
    });
  }

  Future<void> _navigateToStoryOwnerChat(String replyText) async {
    try {
      // Navigate to chat with the story owner
      await VChatController.I.roomApi.openChatWith(
        peerId: widget.storyModel.userData.id,
      );

      // Close the story view after navigating to chat
      if (mounted) {
        context.pop();
      }
    } catch (e) {
      VAppAlert.showErrorSnackBar(
        message: "Failed to open chat",
        context: context,
      );
    }
  }

  void _parseStories() {
    for (final story in widget.storyModel.stories) {
      if (story.storyType == StoryType.image) {
        stories.add(
          StoryItem.pageImage(
            url: VPlatformFile.fromUrl(networkUrl: story.att!['url']!)
                .fullNetworkUrl!,
            controller: controller,
            caption: null, // Remove built-in caption, we'll use custom overlay
            duration: const Duration(seconds: 7),
            imageFit: BoxFit.contain,
          ),
        );
        continue;
      }
      if (story.storyType == StoryType.video) {
        stories.add(
          StoryItem.pageVideo(
            VPlatformFile.fromUrl(networkUrl: story.att!['url']!)
                .fullNetworkUrl!,
            controller: controller,
            caption: null, // Remove built-in caption, we'll use custom overlay
            duration: const Duration(seconds: 15),
          ),
        );
        continue;
      }
      if (story.storyType == StoryType.text) {
        stories.add(
          StoryItem.text(
            title: story.content,
            duration: const Duration(seconds: 10),
            textStyle: TextStyle(
              color: Colors.white,
              fontSize: 35,
              fontStyle: story.fontType == StoryFontType.italic
                  ? FontStyle.italic
                  : null,
              textBaseline: TextBaseline.alphabetic,
              fontWeight:
                  story.fontType == StoryFontType.bold ? FontWeight.bold : null,
            ),
            backgroundColor: story.colorValue == null
                ? Colors.green
                : Color(story.colorValue!),
          ),
        );
        continue;
      }
    }
  }

  Future<void> _saveToMemories() async {
    // Check if story is already saved
    if (_memoryController.isStorySaved(current.id)) {
      VAppAlert.showErrorSnackBar(
        message: 'Story is already saved to memories',
        context: context,
      );
      return;
    }

    // Show confirmation dialog
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Save to Memories'),
        content: const Text(
            'Do you want to save this story to your memories? You can view it anytime even after it expires.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Save'),
          ),
        ],
      ),
    );

    if (result == true) {
      // Show loading
      VAppAlert.showLoading(context: context);

      try {
        final success = await _memoryController.saveStoryToMemories(current.id);

        // Hide loading
        if (mounted) {
          Navigator.of(context).pop();
        }

        if (success) {
          VAppAlert.showSuccessSnackBar(
            message: 'Story saved to memories successfully!',
            context: context,
          );
        } else {
          VAppAlert.showErrorSnackBar(
            message: 'Failed to save story to memories',
            context: context,
          );
        }
      } catch (e) {
        // Hide loading
        if (mounted) {
          Navigator.of(context).pop();
        }

        VAppAlert.showErrorSnackBar(
          message: 'Error saving story: ${e.toString()}',
          context: context,
        );
      }
    }
  }

  void _onReplyFocusChanged() {
    // If focus is gained, pause the story
    if (_replyFocusNode.hasFocus) {
      if (!_isTypingReply) {
        setState(() {
          _isTypingReply = true;
        });
        controller.pause();
      }
    }
    // If focus is lost, resume the story
    else if (!_replyFocusNode.hasFocus) {
      if (_isTypingReply) {
        setState(() {
          _isTypingReply = false;
        });
        controller.play();
      }
    }
  }

  /// Initialize audio components for controlling app audio
  Future<void> _initializeAudioSession() async {
    try {
      // Get current volume for restoration
      _originalVolume = await FlutterVolumeController.getVolume();
    } catch (e) {
      // Audio initialization failed - mute will fall back to visual only
    }
  }

  /// Mute the app's audio output using volume control only
  Future<void> _muteAudio() async {
    try {
      // Get current volume before muting (ensure it's not 0)
      final currentVolume = await FlutterVolumeController.getVolume();

      // If current volume is 0 or very low, set a default restore volume
      if (currentVolume != null && currentVolume <= 0.1) {
        _originalVolume = 0.5; // Default to 50% volume
      } else {
        _originalVolume = currentVolume ?? 0.5;
      }

      // Mute the device
      await FlutterVolumeController.setVolume(0.0);
    } catch (e) {
      // Audio muting failed - will show visual feedback only
      rethrow;
    }
  }

  /// Restore the app's audio output
  Future<void> _restoreAudio() async {
    try {
      // Restore to original volume or default if not set
      final volumeToRestore = _originalVolume ?? 0.5;
      await FlutterVolumeController.setVolume(volumeToRestore);
    } catch (e) {
      // Audio restoration failed - will show visual feedback only
    }
  }
}
