#
# Generated file, do not edit.
#

list(APPEND FLUTTER_PLUGIN_LIST
  agora_rtc_engine
  app_links
  audioplayers_windows
  connectivity_plus
  dynamic_color
  emoji_picker_flutter
  fc_native_video_thumbnail
  file_saver
  file_selector_windows
  firebase_core
  flutter_volume_controller
  gal
  geolocator_windows
  iris_method_channel
  pasteboard
  permission_handler_windows
  quick_notify
  record_windows
  screen_retriever_windows
  share_plus
  sqlite3_flutter_libs
  url_launcher_windows
  window_manager
)

list(APPEND FLUTTER_FFI_PLUGIN_LIST
)

set(PLUGIN_BUNDLED_LIBRARIES)

foreach(plugin ${FLUTTER_PLUGIN_LIST})
  add_subdirectory(flutter/ephemeral/.plugin_symlinks/${plugin}/windows plugins/${plugin})
  target_link_libraries(${BINARY_NAME} PRIVATE ${plugin}_plugin)
  list(APPEND PLUGIN_BUNDLED_LIBRARIES $<TARGET_FILE:${plugin}_plugin>)
  list(APPEND PLUGIN_BUNDLED_LIBRARIES ${${plugin}_bundled_libraries})
endforeach(plugin)

foreach(ffi_plugin ${FLUTTER_FFI_PLUGIN_LIST})
  add_subdirectory(flutter/ephemeral/.plugin_symlinks/${ffi_plugin}/windows plugins/${ffi_plugin})
  list(APPEND PLUGIN_BUNDLED_LIBRARIES ${${ffi_plugin}_bundled_libraries})
endforeach(ffi_plugin)
