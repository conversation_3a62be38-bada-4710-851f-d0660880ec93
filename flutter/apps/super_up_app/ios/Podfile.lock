PODS:
  - agora_rtc_engine (6.5.2):
    - AgoraIrisRTC_iOS (= 4.5.2-build.1)
    - AgoraRtcEngine_iOS (= 4.5.2)
    - Flutter
  - AgoraInfra_iOS (********)
  - AgoraIrisRTC_iOS (4.5.2-build.1)
  - AgoraRtcEngine_iOS (4.5.2):
    - AgoraRtcEngine_iOS/AIAEC (= 4.5.2)
    - AgoraRtcEngine_iOS/AIAECLL (= 4.5.2)
    - AgoraRtcEngine_iOS/AINS (= 4.5.2)
    - AgoraRtcEngine_iOS/AINSLL (= 4.5.2)
    - AgoraRtcEngine_iOS/AudioBeauty (= 4.5.2)
    - AgoraRtcEngine_iOS/ClearVision (= 4.5.2)
    - AgoraRtcEngine_iOS/ContentInspect (= 4.5.2)
    - AgoraRtcEngine_iOS/FaceCapture (= 4.5.2)
    - AgoraRtcEngine_iOS/FaceDetection (= 4.5.2)
    - AgoraRtcEngine_iOS/LipSync (= 4.5.2)
    - AgoraRtcEngine_iOS/ReplayKit (= 4.5.2)
    - AgoraRtcEngine_iOS/RtcBasic (= 4.5.2)
    - AgoraRtcEngine_iOS/SpatialAudio (= 4.5.2)
    - AgoraRtcEngine_iOS/VideoAv1CodecDec (= 4.5.2)
    - AgoraRtcEngine_iOS/VideoAv1CodecEnc (= 4.5.2)
    - AgoraRtcEngine_iOS/VideoCodecDec (= 4.5.2)
    - AgoraRtcEngine_iOS/VideoCodecEnc (= 4.5.2)
    - AgoraRtcEngine_iOS/VirtualBackground (= 4.5.2)
    - AgoraRtcEngine_iOS/VQA (= 4.5.2)
  - AgoraRtcEngine_iOS/AIAEC (4.5.2)
  - AgoraRtcEngine_iOS/AIAECLL (4.5.2)
  - AgoraRtcEngine_iOS/AINS (4.5.2)
  - AgoraRtcEngine_iOS/AINSLL (4.5.2)
  - AgoraRtcEngine_iOS/AudioBeauty (4.5.2)
  - AgoraRtcEngine_iOS/ClearVision (4.5.2)
  - AgoraRtcEngine_iOS/ContentInspect (4.5.2)
  - AgoraRtcEngine_iOS/FaceCapture (4.5.2)
  - AgoraRtcEngine_iOS/FaceDetection (4.5.2)
  - AgoraRtcEngine_iOS/LipSync (4.5.2)
  - AgoraRtcEngine_iOS/ReplayKit (4.5.2)
  - AgoraRtcEngine_iOS/RtcBasic (4.5.2):
    - AgoraInfra_iOS (= ********)
  - AgoraRtcEngine_iOS/SpatialAudio (4.5.2)
  - AgoraRtcEngine_iOS/VideoAv1CodecDec (4.5.2)
  - AgoraRtcEngine_iOS/VideoAv1CodecEnc (4.5.2)
  - AgoraRtcEngine_iOS/VideoCodecDec (4.5.2)
  - AgoraRtcEngine_iOS/VideoCodecEnc (4.5.2)
  - AgoraRtcEngine_iOS/VirtualBackground (4.5.2)
  - AgoraRtcEngine_iOS/VQA (4.5.2)
  - app_badge_plus (1.2.1):
    - Flutter
  - app_links (0.0.2):
    - Flutter
  - audio_service (0.0.1):
    - Flutter
    - FlutterMacOS
  - audio_session (0.0.1):
    - Flutter
  - audio_waveforms (0.0.1):
    - Flutter
  - audioplayers_darwin (0.0.1):
    - Flutter
    - FlutterMacOS
  - background_downloader (0.0.1):
    - Flutter
  - blurhash (0.0.1):
    - Flutter
  - camera_avfoundation (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - CryptoSwift (1.8.4)
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - emoji_picker_flutter (0.0.1):
    - Flutter
  - eraser (0.0.1):
    - Flutter
  - fc_native_video_thumbnail (0.0.1):
    - Flutter
    - FlutterMacOS
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - file_saver (0.0.1):
    - Flutter
  - Firebase/CoreOnly (11.15.0):
    - FirebaseCore (~> 11.15.0)
  - Firebase/Messaging (11.15.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 11.15.0)
  - firebase_core (3.15.1):
    - Firebase/CoreOnly (= 11.15.0)
    - Flutter
  - firebase_messaging (15.2.9):
    - Firebase/Messaging (= 11.15.0)
    - firebase_core
    - Flutter
  - FirebaseCore (11.15.0):
    - FirebaseCoreInternal (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Logger (~> 8.1)
  - FirebaseCoreInternal (11.15.0):
    - "GoogleUtilities/NSData+zlib (~> 8.1)"
  - FirebaseInstallations (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - PromisesObjC (~> 2.4)
  - FirebaseMessaging (11.15.0):
    - FirebaseCore (~> 11.15.0)
    - FirebaseInstallations (~> 11.0)
    - GoogleDataTransport (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 8.1)
    - GoogleUtilities/Environment (~> 8.1)
    - GoogleUtilities/Reachability (~> 8.1)
    - GoogleUtilities/UserDefaults (~> 8.1)
    - nanopb (~> 3.30910.0)
  - Flutter (1.0.0)
  - flutter_callkit_incoming (0.0.1):
    - CryptoSwift
    - Flutter
  - flutter_fgbg (0.0.1):
    - Flutter
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_ringtone_player (0.0.1):
    - Flutter
  - flutter_vlc_player (3.0.3):
    - Flutter
    - MobileVLCKit (~> 3.6.1b1)
  - flutter_volume_controller (0.0.1):
    - Flutter
  - gal (1.0.0):
    - Flutter
    - FlutterMacOS
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - Google-Maps-iOS-Utils (5.0.0):
    - GoogleMaps (~> 8.0)
  - Google-Mobile-Ads-SDK (11.13.0):
    - GoogleUserMessagingPlatform (>= 1.1)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - Google-Maps-iOS-Utils (< 7.0, >= 5.0)
    - GoogleMaps (< 10.0, >= 8.4)
  - google_mobile_ads (5.3.1):
    - Flutter
    - Google-Mobile-Ads-SDK (~> 11.13.0)
    - webview_flutter_wkwebview
  - GoogleDataTransport (10.1.0):
    - nanopb (~> 3.30910.0)
    - PromisesObjC (~> 2.4)
  - GoogleMaps (8.4.0):
    - GoogleMaps/Maps (= 8.4.0)
  - GoogleMaps/Base (8.4.0)
  - GoogleMaps/Maps (8.4.0):
    - GoogleMaps/Base
  - GoogleUserMessagingPlatform (3.0.0)
  - GoogleUtilities/AppDelegateSwizzler (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (8.1.0):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (8.1.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (8.1.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (8.1.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (8.1.0)
  - GoogleUtilities/Reachability (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (8.1.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.7.4)
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_purchase_storekit (0.0.1):
    - Flutter
    - FlutterMacOS
  - iris_method_channel (0.0.1):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - map_launcher (0.0.1):
    - Flutter
  - MobileVLCKit (3.6.1b1)
  - nanopb (3.30910.0):
    - nanopb/decode (= 3.30910.0)
    - nanopb/encode (= 3.30910.0)
  - nanopb/decode (3.30910.0)
  - nanopb/encode (3.30910.0)
  - open_filex (0.0.2):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - pasteboard (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - photo_manager (2.0.0):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - record_darwin (1.0.0):
    - Flutter
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - sensors_plus (0.0.1):
    - Flutter
  - share_handler_ios (0.0.14):
    - Flutter
    - share_handler_ios/share_handler_ios_models (= 0.0.14)
    - share_handler_ios_models
  - share_handler_ios/share_handler_ios_models (0.0.14):
    - Flutter
    - share_handler_ios_models
  - share_handler_ios_models (0.0.9)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - sqlite3 (3.49.2):
    - sqlite3/common (= 3.49.2)
  - sqlite3/common (3.49.2)
  - sqlite3/dbstatvtab (3.49.2):
    - sqlite3/common
  - sqlite3/fts5 (3.49.2):
    - sqlite3/common
  - sqlite3/math (3.49.2):
    - sqlite3/common
  - sqlite3/perf-threadsafe (3.49.2):
    - sqlite3/common
  - sqlite3/rtree (3.49.2):
    - sqlite3/common
  - sqlite3_flutter_libs (0.0.1):
    - Flutter
    - FlutterMacOS
    - sqlite3 (~> 3.49.1)
    - sqlite3/dbstatvtab
    - sqlite3/fts5
    - sqlite3/math
    - sqlite3/perf-threadsafe
    - sqlite3/rtree
  - SwiftyGif (5.4.5)
  - TOCropViewController (2.7.4)
  - url_launcher_ios (0.0.1):
    - Flutter
  - vibration (1.7.5):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - agora_rtc_engine (from `.symlinks/plugins/agora_rtc_engine/ios`)
  - app_badge_plus (from `.symlinks/plugins/app_badge_plus/ios`)
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - audio_service (from `.symlinks/plugins/audio_service/darwin`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - audio_waveforms (from `.symlinks/plugins/audio_waveforms/ios`)
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/darwin`)
  - background_downloader (from `.symlinks/plugins/background_downloader/ios`)
  - blurhash (from `.symlinks/plugins/blurhash/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - emoji_picker_flutter (from `.symlinks/plugins/emoji_picker_flutter/ios`)
  - eraser (from `.symlinks/plugins/eraser/ios`)
  - fc_native_video_thumbnail (from `.symlinks/plugins/fc_native_video_thumbnail/darwin`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - file_saver (from `.symlinks/plugins/file_saver/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_callkit_incoming (from `.symlinks/plugins/flutter_callkit_incoming/ios`)
  - flutter_fgbg (from `.symlinks/plugins/flutter_fgbg/ios`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_ringtone_player (from `.symlinks/plugins/flutter_ringtone_player/ios`)
  - flutter_vlc_player (from `.symlinks/plugins/flutter_vlc_player/ios`)
  - flutter_volume_controller (from `.symlinks/plugins/flutter_volume_controller/ios`)
  - gal (from `.symlinks/plugins/gal/darwin`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - google_mobile_ads (from `.symlinks/plugins/google_mobile_ads/ios`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_purchase_storekit (from `.symlinks/plugins/in_app_purchase_storekit/darwin`)
  - iris_method_channel (from `.symlinks/plugins/iris_method_channel/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - map_launcher (from `.symlinks/plugins/map_launcher/ios`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - pasteboard (from `.symlinks/plugins/pasteboard/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - record_darwin (from `.symlinks/plugins/record_darwin/ios`)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - share_handler_ios (from `.symlinks/plugins/share_handler_ios/ios`)
  - share_handler_ios_models (from `.symlinks/plugins/share_handler_ios/ios/Models`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - sqlite3_flutter_libs (from `.symlinks/plugins/sqlite3_flutter_libs/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - vibration (from `.symlinks/plugins/vibration/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - AgoraInfra_iOS
    - AgoraIrisRTC_iOS
    - AgoraRtcEngine_iOS
    - CryptoSwift
    - DKImagePickerController
    - DKPhotoGallery
    - Firebase
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - Google-Maps-iOS-Utils
    - Google-Mobile-Ads-SDK
    - GoogleDataTransport
    - GoogleMaps
    - GoogleUserMessagingPlatform
    - GoogleUtilities
    - libwebp
    - Mantle
    - MobileVLCKit
    - nanopb
    - PromisesObjC
    - SDWebImage
    - SDWebImageWebPCoder
    - sqlite3
    - SwiftyGif
    - TOCropViewController

EXTERNAL SOURCES:
  agora_rtc_engine:
    :path: ".symlinks/plugins/agora_rtc_engine/ios"
  app_badge_plus:
    :path: ".symlinks/plugins/app_badge_plus/ios"
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  audio_service:
    :path: ".symlinks/plugins/audio_service/darwin"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  audio_waveforms:
    :path: ".symlinks/plugins/audio_waveforms/ios"
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/darwin"
  background_downloader:
    :path: ".symlinks/plugins/background_downloader/ios"
  blurhash:
    :path: ".symlinks/plugins/blurhash/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  emoji_picker_flutter:
    :path: ".symlinks/plugins/emoji_picker_flutter/ios"
  eraser:
    :path: ".symlinks/plugins/eraser/ios"
  fc_native_video_thumbnail:
    :path: ".symlinks/plugins/fc_native_video_thumbnail/darwin"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  file_saver:
    :path: ".symlinks/plugins/file_saver/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_callkit_incoming:
    :path: ".symlinks/plugins/flutter_callkit_incoming/ios"
  flutter_fgbg:
    :path: ".symlinks/plugins/flutter_fgbg/ios"
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_ringtone_player:
    :path: ".symlinks/plugins/flutter_ringtone_player/ios"
  flutter_vlc_player:
    :path: ".symlinks/plugins/flutter_vlc_player/ios"
  flutter_volume_controller:
    :path: ".symlinks/plugins/flutter_volume_controller/ios"
  gal:
    :path: ".symlinks/plugins/gal/darwin"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  google_mobile_ads:
    :path: ".symlinks/plugins/google_mobile_ads/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_purchase_storekit:
    :path: ".symlinks/plugins/in_app_purchase_storekit/darwin"
  iris_method_channel:
    :path: ".symlinks/plugins/iris_method_channel/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  map_launcher:
    :path: ".symlinks/plugins/map_launcher/ios"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  pasteboard:
    :path: ".symlinks/plugins/pasteboard/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  record_darwin:
    :path: ".symlinks/plugins/record_darwin/ios"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  share_handler_ios:
    :path: ".symlinks/plugins/share_handler_ios/ios"
  share_handler_ios_models:
    :path: ".symlinks/plugins/share_handler_ios/ios/Models"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  sqlite3_flutter_libs:
    :path: ".symlinks/plugins/sqlite3_flutter_libs/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  vibration:
    :path: ".symlinks/plugins/vibration/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  agora_rtc_engine: 6a8683bd2d8473e36f059d3bac5f1f475199d571
  AgoraInfra_iOS: 3691b2b277a1712a35ae96de25af319de0d73d08
  AgoraIrisRTC_iOS: eab58c126439adf5ec99632828a558ea216860da
  AgoraRtcEngine_iOS: 97e2398a2addda9057815a2a583a658e36796ff6
  app_badge_plus: 9f9eb6c683a8993a64727c40bd19f0cab8a4b542
  app_links: 76b66b60cc809390ca1ad69bfd66b998d2387ac7
  audio_service: aa99a6ba2ae7565996015322b0bb024e1d25c6fd
  audio_session: 9bb7f6c970f21241b19f5a3658097ae459681ba0
  audio_waveforms: a6dde7fe7c0ea05f06ffbdb0f7c1b2b2ba6cedcf
  audioplayers_darwin: 4f9ca89d92d3d21cec7ec580e78ca888e5fb68bd
  background_downloader: b42a56120f5348bff70e74222f0e9e6f7f1a1537
  blurhash: 7d0156d68a21f3d4276b71c2fae094d9c0753959
  camera_avfoundation: 04b44aeb14070126c6529e5ab82cc7c9fca107cf
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  CryptoSwift: e64e11850ede528a02a0f3e768cec8e9d92ecb90
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  emoji_picker_flutter: ed468d9746c21711e66b2788880519a9de5de211
  eraser: 83a4b06985f3702aa3d8dec816f9693266012937
  fc_native_video_thumbnail: b511cec81fad66be9b28dd54b9adb39d40fcd6cc
  file_picker: a0560bc09d61de87f12d246fc47d2119e6ef37be
  file_saver: 6cdbcddd690cb02b0c1a0c225b37cd805c2bf8b6
  Firebase: d99ac19b909cd2c548339c2241ecd0d1599ab02e
  firebase_core: ece862f94b2bc72ee0edbeec7ab5c7cb09fe1ab5
  firebase_messaging: e1a5fae495603115be1d0183bc849da748734e2b
  FirebaseCore: efb3893e5b94f32b86e331e3bd6dadf18b66568e
  FirebaseCoreInternal: 9afa45b1159304c963da48addb78275ef701c6b4
  FirebaseInstallations: 317270fec08a5d418fdbc8429282238cab3ac843
  FirebaseMessaging: 3b26e2cee503815e01c3701236b020aa9b576f09
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_callkit_incoming: cb8138af67cda6dd981f7101a5d709003af21502
  flutter_fgbg: d3da78df78454b1808f0829a5da9cd17dfe16444
  flutter_image_compress_common: 1697a328fd72bfb335507c6bca1a65fa5ad87df1
  flutter_local_notifications: 395056b3175ba4f08480a7c5de30cd36d69827e4
  flutter_native_splash: c32d145d68aeda5502d5f543ee38c192065986cf
  flutter_ringtone_player: a77c42464250845611eaa44c27e8714acc800138
  flutter_vlc_player: 5464f594a9338e080d240bbe7b10ffe30c7bc8bc
  flutter_volume_controller: c2be490cb0487e8b88d0d9fc2b7e1c139a4ebccb
  gal: baecd024ebfd13c441269ca7404792a7152fde89
  geolocator_apple: ab36aa0e8b7d7a2d7639b3b4e48308394e8cef5e
  Google-Maps-iOS-Utils: 66d6de12be1ce6d3742a54661e7a79cb317a9321
  Google-Mobile-Ads-SDK: 14f57f2dc33532a24db288897e26494640810407
  google_maps_flutter_ios: 0291eb2aa252298a769b04d075e4a9d747ff7264
  google_mobile_ads: 16ad301354fa6a7ea55770c6254bd4339bf8558b
  GoogleDataTransport: aae35b7ea0c09004c3797d53c8c41f66f219d6a7
  GoogleMaps: 8939898920281c649150e0af74aa291c60f2e77d
  GoogleUserMessagingPlatform: f8d0cdad3ca835406755d0a69aa634f00e76d576
  GoogleUtilities: 00c88b9a86066ef77f0da2fab05f65d7768ed8e1
  image_cropper: 5f162dcf988100dc1513f9c6b7eb42cd6fbf9156
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  in_app_purchase_storekit: d1a48cb0f8b29dbf5f85f782f5dd79b21b90a5e6
  iris_method_channel: b9db2053dac3dc84e256c8792eff6f11323a53bd
  just_audio: 4e391f57b79cad2b0674030a00453ca5ce817eed
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  map_launcher: fe43bda6720bb73c12fcc1bdd86123ff49a4d4d6
  MobileVLCKit: 2d9c7c373393ae43086aeeff890bf0b1afc15c5c
  nanopb: fad817b59e0457d11a5dfbde799381cd727c1275
  open_filex: 432f3cd11432da3e39f47fcc0df2b1603854eff1
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  pasteboard: 49088aeb6119d51f976a421db60d8e1ab079b63c
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  photo_manager: d2fbcc0f2d82458700ee6256a15018210a81d413
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  record_darwin: fb1f375f1d9603714f55b8708a903bbb91ffdb0a
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  sensors_plus: 6a11ed0c2e1d0bd0b20b4029d3bad27d96e0c65b
  share_handler_ios: e2244e990f826b2c8eaa291ac3831569438ba0fb
  share_handler_ios_models: fc638c9b4330dc7f082586c92aee9dfa0b87b871
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  sqlite3: 3c950dc86011117c307eb0b28c4a7bb449dce9f1
  sqlite3_flutter_libs: f6acaa2172e6bb3e2e70c771661905080e8ebcf2
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  vibration: 8e2f50fc35bb736f9eecb7dd9f7047fbb6a6e888
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  wakelock_plus: 04623e3f525556020ebd4034310f20fe7fda8b49
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2

PODFILE CHECKSUM: c99ac770cedeecc5f3420cbc11dc77ee0b265711

COCOAPODS: 1.16.2
